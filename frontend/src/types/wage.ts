/**
 * 工资记录相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 11:30:00 +08:00; Reason: 创建工资记录类型文件, 定义前端数据结构; Principle_Applied: 类型安全;}}
 */

// 工资记录接口
export interface Wage {
  id: number
  enterpriseId: number
  deviceId?: number
  userCode?: number
  loginTime?: string
  logoutTime?: string
  patternId?: number
  patternStitch?: number
  patternWage?: number
  productiveWage?: number
  changePieceCount?: number
  changePieceWage?: number
  changeLineCount?: number
  changeLineWage?: number
  fineAmount?: number
  subsidyAmount?: number
  totalWage?: number
  latheCount?: number
  remark?: string
  createdAt: string
  updatedAt: string
  // 关联数据
  device?: {
    id: number
    name: string
    code?: string
  }
  pattern?: {
    id: number
    name: string
    code?: string
  }
  enterprise?: {
    id: number
    name: string
  }
}

// 工资记录列表查询参数
export interface WageListQuery {
  page?: number
  pageSize?: number
  search?: string
  userCode?: number
  deviceId?: number
  patternId?: number
  startDate?: string
  endDate?: string
}

// 工资记录列表响应
export interface WageListResponse {
  wages: Wage[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 工资记录搜索选项
export interface WageSearchOptions {
  devices: Array<{ id: number; name: string; code?: string }>
  patterns: Array<{ id: number; name: string; code?: string }>
  users: Array<{ userCode: number; realName: string }>
}

// 创建工资记录请求
export interface CreateWageRequest {
  deviceId?: number
  userCode?: number
  loginTime?: string
  logoutTime?: string
  patternId?: number
  patternStitch?: number
  patternWage?: number
  productiveWage?: number
  changePieceCount?: number
  changePieceWage?: number
  changeLineCount?: number
  changeLineWage?: number
  fineAmount?: number
  subsidyAmount?: number
  totalWage?: number
  latheCount?: number
  remark?: string
}

// 更新工资记录请求
export interface UpdateWageRequest extends Partial<CreateWageRequest> {}

// 工资统计数据
export interface WageStatistics {
  totalWages: number
  totalRecords: number
  averageWage: number
  maxWage: number
  minWage: number
  monthlyStats: Array<{
    month: string
    totalWage: number
    recordCount: number
  }>
}

// 工资记录表单数据
export interface WageFormData {
  id?: number
  deviceId?: number
  userCode?: number
  loginTime?: string
  logoutTime?: string
  patternId?: number
  patternStitch?: number
  patternWage?: number
  productiveWage?: number
  changePieceCount?: number
  changePieceWage?: number
  changeLineCount?: number
  changeLineWage?: number
  fineAmount?: number
  subsidyAmount?: number
  totalWage?: number
  latheCount?: number
  remark?: string
}

/**
 * 格式化工资金额
 * @param amount 金额
 * @returns 格式化后的金额字符串
 */
export const formatWageAmount = (amount?: number): string => {
  if (amount === undefined || amount === null) {
    return '¥0.00'
  }
  return `¥${amount.toFixed(2)}`
}

/**
 * 格式化针数
 * @param stitches 针数
 * @returns 格式化后的针数字符串
 */
export const formatStitches = (stitches?: number): string => {
  if (stitches === undefined || stitches === null) {
    return '0针'
  }
  if (stitches >= 10000) {
    return `${(stitches / 10000).toFixed(1)}万针`
  }
  return `${stitches}针`
}

/**
 * 计算工作时长
 * @param loginTime 上机时间
 * @param logoutTime 下机时间
 * @returns 工作时长（小时）
 */
export const calculateWorkDuration = (loginTime?: string, logoutTime?: string): number => {
  if (!loginTime || !logoutTime) {
    return 0
  }
  const start = new Date(loginTime)
  const end = new Date(logoutTime)
  const diffMs = end.getTime() - start.getTime()
  return Math.max(0, diffMs / (1000 * 60 * 60)) // 转换为小时
}

/**
 * 格式化工作时长
 * @param loginTime 上机时间
 * @param logoutTime 下机时间
 * @returns 格式化后的工作时长字符串
 */
export const formatWorkDuration = (loginTime?: string, logoutTime?: string): string => {
  const duration = calculateWorkDuration(loginTime, logoutTime)
  if (duration === 0) {
    return '-'
  }
  const hours = Math.floor(duration)
  const minutes = Math.round((duration - hours) * 60)
  if (hours === 0) {
    return `${minutes}分钟`
  }
  if (minutes === 0) {
    return `${hours}小时`
  }
  return `${hours}小时${minutes}分钟`
}

/**
 * 验证工资记录表单数据
 * @param data 表单数据
 * @returns 验证结果
 */
export const validateWageForm = (data: WageFormData): { valid: boolean; errors: string[] } => {
  const errors: string[] = []

  // 验证员工编码
  if (data.userCode !== undefined && data.userCode <= 0) {
    errors.push('员工编码必须大于0')
  }

  // 验证花样针数
  if (data.patternStitch !== undefined && data.patternStitch < 0) {
    errors.push('花样针数不能为负数')
  }

  // 验证工价
  if (data.patternWage !== undefined && data.patternWage < 0) {
    errors.push('花样工价不能为负数')
  }

  // 验证换片次数
  if (data.changePieceCount !== undefined && data.changePieceCount < 0) {
    errors.push('换片次数不能为负数')
  }

  // 验证换线次数
  if (data.changeLineCount !== undefined && data.changeLineCount < 0) {
    errors.push('换线次数不能为负数')
  }

  // 验证金额字段
  const amountFields = ['productiveWage', 'changePieceWage', 'changeLineWage', 'fineAmount', 'subsidyAmount', 'totalWage']
  amountFields.forEach(field => {
    const value = data[field as keyof WageFormData] as number
    if (value !== undefined && value < 0) {
      const fieldNames: Record<string, string> = {
        productiveWage: '产量工资',
        changePieceWage: '换片工资',
        changeLineWage: '换线工资',
        fineAmount: '罚款金额',
        subsidyAmount: '奖励金额',
        totalWage: '总工资'
      }
      errors.push(`${fieldNames[field]}不能为负数`)
    }
  })

  // 验证车数
  if (data.latheCount !== undefined && data.latheCount < 0) {
    errors.push('车数不能为负数')
  }

  // 验证时间逻辑
  if (data.loginTime && data.logoutTime) {
    const loginDate = new Date(data.loginTime)
    const logoutDate = new Date(data.logoutTime)
    if (logoutDate <= loginDate) {
      errors.push('下机时间必须晚于上机时间')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 创建默认工资记录表单数据
 * @returns 默认表单数据
 */
export const createDefaultWageFormData = (): WageFormData => ({
  deviceId: undefined,
  userCode: undefined,
  loginTime: undefined,
  logoutTime: undefined,
  patternId: undefined,
  patternStitch: 0,
  patternWage: 0,
  productiveWage: 0,
  changePieceCount: 0,
  changePieceWage: 0,
  changeLineCount: 0,
  changeLineWage: 0,
  fineAmount: 0,
  subsidyAmount: 0,
  totalWage: 0,
  latheCount: 0,
  remark: ''
})
