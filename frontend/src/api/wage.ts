/**
 * 工资记录管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 11:45:00 +08:00; Reason: 创建工资记录API接口, 调用后端服务; Principle_Applied: API封装;}}
 */

import { http } from './request'
import type {
  Wage,
  WageListQuery,
  WageListResponse,
  WageSearchOptions,
  CreateWageRequest,
  UpdateWageRequest,
  WageStatistics
} from '../types/wage'

/**
 * 工资记录管理API
 */
export const wageApi = {
  /**
   * 获取工资记录列表
   * @param params 查询参数
   * @returns 工资记录列表响应
   */
  getWageList(params: WageListQuery): Promise<WageListResponse> {
    return http.get('/wages', { params })
  },

  /**
   * 获取工资记录详情
   * @param id 工资记录ID
   * @returns 工资记录详情
   */
  getWageById(id: number): Promise<Wage> {
    return http.get(`/wages/${id}`)
  },

  /**
   * 创建工资记录
   * @param data 创建请求数据
   * @returns 创建的工资记录
   */
  createWage(data: CreateWageRequest): Promise<Wage> {
    return http.post('/wages', data)
  },

  /**
   * 更新工资记录
   * @param id 工资记录ID
   * @param data 更新请求数据
   * @returns 更新的工资记录
   */
  updateWage(id: number, data: UpdateWageRequest): Promise<Wage> {
    return http.put(`/wages/${id}`, data)
  },

  /**
   * 删除工资记录
   * @param id 工资记录ID
   * @returns 删除结果
   */
  deleteWage(id: number): Promise<void> {
    return http.delete(`/wages/${id}`)
  },

  /**
   * 批量删除工资记录
   * @param ids 工资记录ID数组
   * @returns 删除结果
   */
  batchDeleteWages(ids: number[]): Promise<void> {
    return http.post('/wages/batch/delete', { ids })
  },

  /**
   * 获取搜索选项
   * @returns 搜索选项数据
   */
  getSearchOptions(): Promise<WageSearchOptions> {
    return http.get('/wages/search-options')
  },

  /**
   * 获取工资统计数据
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 统计数据
   */
  getWageStatistics(startDate?: string, endDate?: string): Promise<WageStatistics> {
    const params: any = {}
    if (startDate) params.startDate = startDate
    if (endDate) params.endDate = endDate
    return http.get('/wages/statistics', { params })
  },

  /**
   * 导出工资记录
   * @param params 查询参数
   * @returns 导出文件
   */
  exportWages(params: WageListQuery): Promise<Blob> {
    return http.get('/wages/export', { 
      params,
      responseType: 'blob'
    })
  }
}

// 重新导出类型
export type {
  Wage,
  WageListQuery,
  WageListResponse,
  WageSearchOptions,
  CreateWageRequest,
  UpdateWageRequest,
  WageStatistics
} from '../types/wage'

export {
  formatWageAmount,
  formatStitches,
  formatWorkDuration,
  calculateWorkDuration,
  validateWageForm,
  createDefaultWageFormData
} from '../types/wage'
