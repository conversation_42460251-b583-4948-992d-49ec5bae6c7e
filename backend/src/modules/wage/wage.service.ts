/**
 * 工资记录服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 10:45:00 +08:00; Reason: 创建工资记录服务, 实现工资列表CRUD功能; Principle_Applied: 服务层模式;}}
 */

import { Op, WhereOptions } from 'sequelize';
import { Wage, WageAttributes, WageCreationAttributes } from '../../shared/database/models/Wage';
import { Device } from '../../shared/database/models/Device';
import { Pattern } from '../../shared/database/models/Pattern';
import { User } from '../../shared/database/models/User';
import { Enterprise } from '../../shared/database/models/Enterprise';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 工资记录列表查询参数
export interface WageListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  userCode?: number;
  deviceId?: number;
  patternId?: number;
  startDate?: string;
  endDate?: string;
  enterpriseId: number;
}

// 工资记录列表响应
export interface WageListResponse {
  wages: Wage[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 工资记录搜索选项
export interface WageSearchOptions {
  devices: Array<{ id: number; name: string; code?: string }>;
  patterns: Array<{ id: number; name: string; code?: string }>;
  users: Array<{ userCode: number; realName: string }>;
}

// 创建工资记录请求
export interface CreateWageRequest {
  deviceId?: number;
  userCode?: number;
  loginTime?: Date;
  logoutTime?: Date;
  patternId?: number;
  patternStitch?: number;
  patternWage?: number;
  productiveWage?: number;
  changePieceCount?: number;
  changePieceWage?: number;
  changeLineCount?: number;
  changeLineWage?: number;
  fineAmount?: number;
  subsidyAmount?: number;
  totalWage?: number;
  latheCount?: number;
  remark?: string;
}

// 更新工资记录请求
export interface UpdateWageRequest extends Partial<CreateWageRequest> {}

// 工资统计数据
export interface WageStatistics {
  totalWages: number;
  totalRecords: number;
  averageWage: number;
  maxWage: number;
  minWage: number;
  monthlyStats: Array<{
    month: string;
    totalWage: number;
    recordCount: number;
  }>;
}

/**
 * 工资记录服务类
 */
export class WageService {
  /**
   * 获取工资记录列表
   */
  async getWageList(query: WageListQuery): Promise<WageListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        userCode,
        deviceId,
        patternId,
        startDate,
        endDate,
        enterpriseId
      } = query;

      const offset = (page - 1) * pageSize;
      const limit = pageSize;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { '$device.name$': { [Op.iLike]: `%${search}%` } },
          { '$device.code$': { [Op.iLike]: `%${search}%` } },
          { '$pattern.name$': { [Op.iLike]: `%${search}%` } },
          { '$pattern.code$': { [Op.iLike]: `%${search}%` } },
          { remark: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 筛选条件
      if (userCode) {
        whereConditions.userCode = userCode;
      }
      if (deviceId) {
        whereConditions.deviceId = deviceId;
      }
      if (patternId) {
        whereConditions.patternId = patternId;
      }

      // 时间范围筛选
      if (startDate || endDate) {
        const dateConditions: any = {};
        if (startDate) {
          dateConditions[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          dateConditions[Op.lte] = new Date(endDate);
        }
        whereConditions.loginTime = dateConditions;
      }

      // 查询工资记录列表
      const { rows: wages, count: total } = await Wage.findAndCountAll({
        where: whereConditions,
        offset,
        limit,
        order: [['createdAt', 'DESC']],
        include: [
          {
            association: 'device',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'enterprise',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      const totalPages = Math.ceil(total / pageSize);

      logger.info('获取工资记录列表成功', {
        enterpriseId,
        total,
        page,
        pageSize
      });

      return {
        wages,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取工资记录列表失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取工资记录详情
   */
  async getWageById(id: number, enterpriseId: number): Promise<Wage> {
    try {
      const wage = await Wage.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'device',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'pattern',
            attributes: ['id', 'name', 'code'],
            required: false,
          },
          {
            association: 'enterprise',
            attributes: ['id', 'name'],
            required: false,
          }
        ]
      });

      if (!wage) {
        throw createApiError(404, '工资记录不存在');
      }

      logger.info('获取工资记录详情成功', {
        id,
        enterpriseId
      });

      return wage;
    } catch (error) {
      logger.error('获取工资记录详情失败', {
        id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建工资记录
   */
  async createWage(data: CreateWageRequest, enterpriseId: number): Promise<Wage> {
    try {
      // 计算总工资
      const calculatedData = this.calculateTotalWage(data);

      const wage = await Wage.create({
        ...calculatedData,
        enterpriseId
      });

      logger.info('创建工资记录成功', {
        id: wage.id,
        userCode: wage.userCode,
        totalWage: wage.totalWage,
        enterpriseId
      });

      return wage;
    } catch (error) {
      logger.error('创建工资记录失败', {
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新工资记录
   */
  async updateWage(id: number, data: UpdateWageRequest, enterpriseId: number): Promise<Wage> {
    try {
      const wage = await Wage.findOne({
        where: { id, enterpriseId }
      });

      if (!wage) {
        throw createApiError(404, '工资记录不存在');
      }

      // 计算总工资
      const calculatedData = this.calculateTotalWage(data);

      await wage.update(calculatedData);

      logger.info('更新工资记录成功', {
        id,
        userCode: wage.userCode,
        totalWage: wage.totalWage,
        enterpriseId
      });

      return wage;
    } catch (error) {
      logger.error('更新工资记录失败', {
        id,
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除工资记录
   */
  async deleteWage(id: number, enterpriseId: number): Promise<void> {
    try {
      const wage = await Wage.findOne({
        where: { id, enterpriseId }
      });

      if (!wage) {
        throw createApiError(404, '工资记录不存在');
      }

      await wage.destroy();

      logger.info('删除工资记录成功', {
        id,
        userCode: wage.userCode,
        enterpriseId
      });
    } catch (error) {
      logger.error('删除工资记录失败', {
        id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 计算总工资
   * 总工资 = 产量工资 + 奖励金额 + 换片工资 + 换线工资 - 罚款金额
   */
  private calculateTotalWage(data: CreateWageRequest | UpdateWageRequest): CreateWageRequest | UpdateWageRequest {
    const result = { ...data };

    // 计算产量工资
    if (result.patternStitch && result.patternWage) {
      result.productiveWage = (result.patternStitch * result.patternWage) / 10000; // 万针计算
    }

    // 计算总工资
    const productiveWage = result.productiveWage || 0;
    const subsidyAmount = result.subsidyAmount || 0;
    const changePieceWage = result.changePieceWage || 0;
    const changeLineWage = result.changeLineWage || 0;
    const fineAmount = result.fineAmount || 0;

    result.totalWage = productiveWage + subsidyAmount + changePieceWage + changeLineWage - fineAmount;

    return result;
  }

  /**
   * 获取搜索选项
   */
  async getSearchOptions(enterpriseId: number): Promise<WageSearchOptions> {
    try {
      // 获取设备选项
      const devices = await Device.findAll({
        where: { enterpriseId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']]
      });

      // 获取花样选项
      const patterns = await Pattern.findAll({
        where: { enterpriseId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']]
      });

      // 获取用户选项（从工资记录中获取已有的用户编码）
      const userCodes = await Wage.findAll({
        where: {
          enterpriseId,
          userCode: { [Op.not]: null }
        },
        attributes: ['userCode'],
        group: ['userCode'],
        order: [['userCode', 'ASC']]
      });

      // 由于 userCode 是独立的编码字段，不是 User 表的字段，
      // 我们只返回从工资记录中获取的用户编码
      const users = userCodes.map(wage => ({
        userCode: wage.userCode!,
        realName: `员工${wage.userCode}` // 临时显示名称，实际应该从其他地方获取
      }));

      return {
        devices: devices.map(device => ({
          id: device.id,
          name: device.name,
          code: device.code
        })),
        patterns: patterns.map(pattern => ({
          id: pattern.id,
          name: pattern.name,
          code: pattern.code
        })),
        users: users
      };
    } catch (error) {
      logger.error('获取搜索选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取工资统计数据
   */
  async getWageStatistics(enterpriseId: number, startDate?: string, endDate?: string): Promise<WageStatistics> {
    try {
      const whereConditions: WhereOptions = {
        enterpriseId,
        totalWage: { [Op.not]: null }
      };

      // 时间范围筛选
      if (startDate || endDate) {
        const dateConditions: any = {};
        if (startDate) {
          dateConditions[Op.gte] = new Date(startDate);
        }
        if (endDate) {
          dateConditions[Op.lte] = new Date(endDate);
        }
        whereConditions.loginTime = dateConditions;
      }

      // 获取基础统计数据
      const wages = await Wage.findAll({
        where: whereConditions,
        attributes: ['totalWage', 'loginTime'],
        order: [['loginTime', 'ASC']]
      });

      const totalWages = wages.reduce((sum, wage) => sum + (wage.totalWage || 0), 0);
      const totalRecords = wages.length;
      const averageWage = totalRecords > 0 ? totalWages / totalRecords : 0;
      const wageAmounts = wages.map(w => w.totalWage || 0).filter(w => w > 0);
      const maxWage = wageAmounts.length > 0 ? Math.max(...wageAmounts) : 0;
      const minWage = wageAmounts.length > 0 ? Math.min(...wageAmounts) : 0;

      // 按月统计
      const monthlyStatsMap = new Map<string, { totalWage: number; recordCount: number }>();
      wages.forEach(wage => {
        if (wage.loginTime && wage.totalWage) {
          const month = wage.loginTime.toISOString().substring(0, 7); // YYYY-MM
          const existing = monthlyStatsMap.get(month) || { totalWage: 0, recordCount: 0 };
          existing.totalWage += wage.totalWage;
          existing.recordCount += 1;
          monthlyStatsMap.set(month, existing);
        }
      });

      const monthlyStats = Array.from(monthlyStatsMap.entries()).map(([month, stats]) => ({
        month,
        totalWage: stats.totalWage,
        recordCount: stats.recordCount
      }));

      logger.info('获取工资统计数据成功', {
        enterpriseId,
        totalRecords,
        totalWages
      });

      return {
        totalWages,
        totalRecords,
        averageWage,
        maxWage,
        minWage,
        monthlyStats
      };
    } catch (error) {
      logger.error('获取工资统计数据失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 批量删除工资记录
   */
  async batchDeleteWages(ids: number[], enterpriseId: number): Promise<void> {
    try {
      const deletedCount = await Wage.destroy({
        where: {
          id: { [Op.in]: ids },
          enterpriseId
        }
      });

      if (deletedCount === 0) {
        throw createApiError(404, '未找到要删除的工资记录');
      }

      logger.info('批量删除工资记录成功', {
        deletedCount,
        enterpriseId
      });
    } catch (error) {
      logger.error('批量删除工资记录失败', {
        ids,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 导出工资记录
   */
  async exportWages(query: WageListQuery): Promise<Buffer> {
    try {
      // 获取所有符合条件的工资记录
      const allWages = await this.getAllWagesForExport(query);

      // 使用 xlsx 库创建 Excel 文件
      const XLSX = require('xlsx');

      // 准备导出数据
      const exportData = allWages.map(wage => ({
        '员工编码': wage.userCode || '',
        '设备名称': wage.device?.name || '',
        '设备编码': wage.device?.code || '',
        '花样名称': wage.pattern?.name || '',
        '花样编码': wage.pattern?.code || '',
        '上机时间': wage.loginTime ? new Date(wage.loginTime).toLocaleString('zh-CN') : '',
        '下机时间': wage.logoutTime ? new Date(wage.logoutTime).toLocaleString('zh-CN') : '',
        '花样针数': wage.patternStitch || 0,
        '花样工价(元/万针)': wage.patternWage || 0,
        '产量工资': wage.productiveWage || 0,
        '换片次数': wage.changePieceCount || 0,
        '换片工资': wage.changePieceWage || 0,
        '换线次数': wage.changeLineCount || 0,
        '换线工资': wage.changeLineWage || 0,
        '罚款金额': wage.fineAmount || 0,
        '奖励金额': wage.subsidyAmount || 0,
        '总工资': wage.totalWage || 0,
        '车数': wage.latheCount || 0,
        '备注': wage.remark || '',
        '创建时间': new Date(wage.createdAt).toLocaleString('zh-CN')
      }));

      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 10 }, // 员工编码
        { wch: 15 }, // 设备名称
        { wch: 12 }, // 设备编码
        { wch: 15 }, // 花样名称
        { wch: 12 }, // 花样编码
        { wch: 18 }, // 上机时间
        { wch: 18 }, // 下机时间
        { wch: 12 }, // 花样针数
        { wch: 15 }, // 花样工价
        { wch: 12 }, // 产量工资
        { wch: 10 }, // 换片次数
        { wch: 12 }, // 换片工资
        { wch: 10 }, // 换线次数
        { wch: 12 }, // 换线工资
        { wch: 12 }, // 罚款金额
        { wch: 12 }, // 奖励金额
        { wch: 12 }, // 总工资
        { wch: 8 },  // 车数
        { wch: 20 }, // 备注
        { wch: 18 }  // 创建时间
      ];
      worksheet['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '工资记录');

      // 生成 Excel 文件缓冲区
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      logger.info('导出工资记录成功', {
        enterpriseId: query.enterpriseId,
        recordCount: allWages.length
      });

      return buffer;
    } catch (error) {
      logger.error('导出工资记录失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取所有符合条件的工资记录用于导出
   */
  private async getAllWagesForExport(query: WageListQuery): Promise<Wage[]> {
    const {
      search,
      userCode,
      deviceId,
      patternId,
      startDate,
      endDate,
      enterpriseId
    } = query;

    // 构建查询条件
    const whereConditions: WhereOptions = {
      enterpriseId
    };

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { '$device.name$': { [Op.iLike]: `%${search}%` } },
        { '$device.code$': { [Op.iLike]: `%${search}%` } },
        { '$pattern.name$': { [Op.iLike]: `%${search}%` } },
        { '$pattern.code$': { [Op.iLike]: `%${search}%` } },
        { remark: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 筛选条件
    if (userCode) {
      whereConditions.userCode = userCode;
    }
    if (deviceId) {
      whereConditions.deviceId = deviceId;
    }
    if (patternId) {
      whereConditions.patternId = patternId;
    }

    // 时间范围筛选
    if (startDate || endDate) {
      const dateConditions: any = {};
      if (startDate) {
        dateConditions[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        dateConditions[Op.lte] = new Date(endDate);
      }
      whereConditions.loginTime = dateConditions;
    }

    // 查询所有符合条件的工资记录
    const wages = await Wage.findAll({
      where: whereConditions,
      order: [['createdAt', 'DESC']],
      include: [
        {
          association: 'device',
          attributes: ['id', 'name', 'code'],
          required: false,
        },
        {
          association: 'pattern',
          attributes: ['id', 'name', 'code'],
          required: false,
        }
      ]
    });

    return wages;
  }
}
