/**
 * 工价配置验证规则
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:15:00 +08:00; Reason: Task-009 添加数据验证和错误处理, 完善后端数据验证规则; Principle_Applied: 数据验证;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 18:30:00 +08:00; Reason: 修复验证库问题, 改用express-validator替代Joi; Principle_Applied: 项目一致性;}}
 */

import { body, param, query } from 'express-validator'
import { WageType } from '../../shared/database/models/WageConfig'

/**
 * 工价配置验证规则
 */
export const wageConfigValidationRules = {
  /**
   * 获取工价配置列表验证规则
   */
  getWageConfigList: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是大于0的整数'),
    query('pageSize')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须是1-100之间的整数'),
    query('search')
      .optional()
      .isLength({ max: 100 })
      .withMessage('搜索关键词长度不能超过100个字符'),
    query('type')
      .optional()
      .isInt()
      .custom((value) => {
        const validTypes = Object.values(WageType).filter(v => typeof v === 'number')
        if (!validTypes.includes(value)) {
          throw new Error('工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)')
        }
        return true
      }),
    query('patternId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('花样ID必须是大于0的整数'),
    query('createdUserId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('创建人ID必须是大于0的整数')
  ],

  /**
   * 获取工价配置详情验证规则
   */
  getWageConfigById: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('工价配置ID必须是大于0的整数')
  ],

  /**
   * 创建工价配置验证规则
   */
  createWageConfig: [
    body('type')
      .isInt()
      .custom((value) => {
        const validTypes = Object.values(WageType).filter(v => typeof v === 'number')
        if (!validTypes.includes(value)) {
          throw new Error('工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)')
        }
        return true
      }),
    body('patternId')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .isInt({ min: 1 })
      .withMessage('花样工价类型必须选择花样'),
    body('patternWage')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .optional()
      .isFloat({ min: 0 })
      .withMessage('花样工价不能为负数'),
    body('incentiveWage')
      .if(body('type').equals(WageType.PATTERN_WAGE))
      .optional()
      .isArray({ max: 20 })
      .withMessage('激励工价规则最多不能超过20条')
      .custom((value) => {
        if (!Array.isArray(value) || value.length === 0) {
          return true
        }

        // 验证每个激励工价项目
        for (let i = 0; i < value.length; i++) {
          const item = value[i]
          if (!item.cumulative || !Number.isInteger(item.cumulative) || item.cumulative <= 0) {
            throw new Error(`第${i + 1}条规则的累计针数必须是大于0的整数`)
          }
          if (item.reward === undefined || item.reward < 0) {
            throw new Error(`第${i + 1}条规则的奖励金额不能为负数`)
          }
        }

        // 检查累计针数是否递增
        for (let i = 1; i < value.length; i++) {
          if (value[i].cumulative <= value[i - 1].cumulative) {
            throw new Error(`第${i + 1}条规则的累计针数必须大于前一条规则`)
          }
        }

        return true
      }),
    body('changePieceWage')
      .if(body('type').equals(WageType.CHANGE_PIECE_WAGE))
      .isFloat({ min: 0 })
      .withMessage('换片单价不能为负数'),
    body('changeLineWage')
      .if(body('type').equals(WageType.CHANGE_LINE_WAGE))
      .isFloat({ min: 0 })
      .withMessage('换线单价不能为负数')
  ],

  /**
   * 更新工价配置验证规则
   */
  updateWageConfig: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('工价配置ID必须是大于0的整数'),
    body('type')
      .optional()
      .isInt()
      .custom((value) => {
        if (value !== undefined) {
          const validTypes = Object.values(WageType).filter(v => typeof v === 'number')
          if (!validTypes.includes(value)) {
            throw new Error('工价类型必须是有效值(1-花样工价, 2-换片单价, 3-换线单价)')
          }
        }
        return true
      }),
    body('patternWage')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('花样工价不能为负数'),
    body('changePieceWage')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('换片单价不能为负数'),
    body('changeLineWage')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('换线单价不能为负数')
  ],

  /**
   * 删除工价配置验证规则
   */
  deleteWageConfig: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('工价配置ID必须是大于0的整数')
  ]
}

/**
 * 业务规则验证函数
 */


