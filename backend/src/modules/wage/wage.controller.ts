/**
 * 工资记录控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 11:00:00 +08:00; Reason: 创建工资记录控制器, 处理HTTP请求; Principle_Applied: 控制器模式;}}
 */

import { Request, Response } from 'express';
import { WageService, WageListQuery, CreateWageRequest, UpdateWageRequest } from './wage.service';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

const wageService = new WageService();

/**
 * 工资记录控制器类
 */
export class WageController {
  /**
   * 获取工资记录列表
   */
  public getWageList = async (req: Request, res: Response): Promise<void> => {
    try {
      const query: WageListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        userCode: req.query.userCode ? parseInt(req.query.userCode as string) : undefined,
        deviceId: req.query.deviceId ? parseInt(req.query.deviceId as string) : undefined,
        patternId: req.query.patternId ? parseInt(req.query.patternId as string) : undefined,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await wageService.getWageList(query);

      res.json({
        code: 200,
        message: '获取工资记录列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取工资记录列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取工资记录详情
   */
  public getWageById = async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '工资记录ID格式错误'
        });
      }

      const wage = await wageService.getWageById(id, enterpriseId);

      res.json({
        code: 200,
        message: '获取工资记录详情成功',
        data: wage
      });
    } catch (error) {
      logger.error('获取工资记录详情失败', {
        wageId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建工资记录
   */
  public createWage = async (req: Request, res: Response): Promise<void> => {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const data: CreateWageRequest = req.body;

      // 基本数据验证
      if (data.userCode && data.userCode <= 0) {
        return res.status(400).json({
          code: 400,
          message: '员工编码必须大于0'
        });
      }

      if (data.patternStitch && data.patternStitch < 0) {
        return res.status(400).json({
          code: 400,
          message: '花样针数不能为负数'
        });
      }

      if (data.totalWage && data.totalWage < 0) {
        return res.status(400).json({
          code: 400,
          message: '总工资不能为负数'
        });
      }

      const wage = await wageService.createWage(data, enterpriseId);

      res.status(201).json({
        code: 201,
        message: '创建工资记录成功',
        data: wage
      });
    } catch (error) {
      logger.error('创建工资记录失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新工资记录
   */
  public updateWage = async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateWageRequest = req.body;

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '工资记录ID格式错误'
        });
      }

      // 基本数据验证
      if (data.userCode && data.userCode <= 0) {
        return res.status(400).json({
          code: 400,
          message: '员工编码必须大于0'
        });
      }

      if (data.patternStitch && data.patternStitch < 0) {
        return res.status(400).json({
          code: 400,
          message: '花样针数不能为负数'
        });
      }

      if (data.totalWage && data.totalWage < 0) {
        return res.status(400).json({
          code: 400,
          message: '总工资不能为负数'
        });
      }

      const wage = await wageService.updateWage(id, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新工资记录成功',
        data: wage
      });
    } catch (error) {
      logger.error('更新工资记录失败', {
        wageId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        data: req.body,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除工资记录
   */
  public deleteWage = async (req: Request, res: Response): Promise<void> => {
    try {
      const id = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      if (isNaN(id)) {
        return res.status(400).json({
          code: 400,
          message: '工资记录ID格式错误'
        });
      }

      await wageService.deleteWage(id, enterpriseId);

      res.json({
        code: 200,
        message: '删除工资记录成功'
      });
    } catch (error) {
      logger.error('删除工资记录失败', {
        wageId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取搜索选项
   */
  public getSearchOptions = async (req: Request, res: Response): Promise<void> => {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const options = await wageService.getSearchOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取搜索选项成功',
        data: options
      });
    } catch (error) {
      logger.error('获取搜索选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取工资统计数据
   */
  public getWageStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      const statistics = await wageService.getWageStatistics(enterpriseId, startDate, endDate);

      res.json({
        code: 200,
        message: '获取工资统计数据成功',
        data: statistics
      });
    } catch (error) {
      logger.error('获取工资统计数据失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 批量删除工资记录
   */
  public batchDeleteWages = async (req: Request, res: Response): Promise<void> => {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const { ids } = req.body;

      if (!Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '请选择要删除的工资记录'
        });
      }

      // 验证ID格式
      const validIds = ids.filter(id => Number.isInteger(id) && id > 0);
      if (validIds.length !== ids.length) {
        return res.status(400).json({
          code: 400,
          message: '工资记录ID格式错误'
        });
      }

      await wageService.batchDeleteWages(validIds, enterpriseId);

      res.json({
        code: 200,
        message: '批量删除工资记录成功'
      });
    } catch (error) {
      logger.error('批量删除工资记录失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        ids: req.body.ids,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 导出工资记录
   */
  public exportWages = async (req: Request, res: Response): Promise<void> => {
    try {
      const enterpriseId = req.user!.enterpriseId;
      const query: WageListQuery = {
        search: req.query.search as string,
        userCode: req.query.userCode ? parseInt(req.query.userCode as string) : undefined,
        deviceId: req.query.deviceId ? parseInt(req.query.deviceId as string) : undefined,
        patternId: req.query.patternId ? parseInt(req.query.patternId as string) : undefined,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        enterpriseId
      };

      const buffer = await wageService.exportWages(query);

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="wages_${new Date().toISOString().split('T')[0]}.xlsx"`);
      res.send(buffer);
    } catch (error) {
      logger.error('导出工资记录失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

// 导出控制器实例
export const wageController = new WageController();
