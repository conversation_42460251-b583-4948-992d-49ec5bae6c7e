import { http } from './request'

// 花样接口
export interface Pattern {
  id: number
  enterpriseId: number
  code?: string
  name: string
  remark?: string
  groupId?: number
  fileType?: string
  stitch?: number
  jumps?: number
  trim?: number
  colors?: number
  baseLine?: number
  surfaceLine?: number
  goldPieceNum?: number
  minX?: number
  maxX?: number
  minY?: number
  maxY?: number
  width?: number
  height?: number
  goldPieceLine?: string
  needle?: string
  mergeGoldPieceLine?: string
  image?: string
  createUser?: string
  createdAt: string
  updatedAt: string
  group?: {
    id: number
    name: string
  }
}

// 花样列表查询参数
export interface PatternListQuery {
  page?: number
  pageSize?: number
  search?: string
  groupId?: number
  fileType?: string
  createUser?: string
}

// 花样列表响应
export interface PatternListResponse {
  patterns: Pattern[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 花样搜索选项
export interface PatternSearchOptions {
  groups: Array<{ id: number; name: string }>
  fileTypes: Array<{ label: string; value: string }>
  createUsers: Array<{ label: string; value: string }>
}

// 花样统计信息
export interface PatternStats {
  total: number
  byFileType: Array<{ fileType: string; count: number }>
  byGroup: Array<{ groupName: string; count: number }>
  byCreateUser: Array<{ createUser: string; count: number }>
}

// 创建花样请求
export interface CreatePatternRequest {
  code?: string
  name: string
  remark?: string
  groupId?: number
  fileType?: string
  stitch?: number
  jumps?: number
  trim?: number
  colors?: number
  baseLine?: number
  surfaceLine?: number
  goldPieceNum?: number
  minX?: number
  maxX?: number
  minY?: number
  maxY?: number
  width?: number
  height?: number
  goldPieceLine?: string
  needle?: string
  mergeGoldPieceLine?: string
  image?: string
  createUser?: string
}

// 更新花样请求
export interface UpdatePatternRequest extends Partial<CreatePatternRequest> {}

/**
 * 花样管理API
 */
export const patternApi = {
  /**
   * 获取花样列表
   */
  getPatternList(params: PatternListQuery): Promise<PatternListResponse> {
    return http.get('/patterns', { params })
  },

  /**
   * 获取花样详情
   */
  getPatternById(id: number): Promise<Pattern> {
    return http.get(`/patterns/${id}`)
  },

  /**
   * 创建花样
   */
  createPattern(data: CreatePatternRequest): Promise<Pattern> {
    return http.post('/patterns', data)
  },

  /**
   * 更新花样
   */
  updatePattern(id: number, data: UpdatePatternRequest): Promise<Pattern> {
    return http.put(`/patterns/${id}`, data)
  },

  /**
   * 删除花样
   */
  deletePattern(id: number): Promise<void> {
    return http.delete(`/patterns/${id}`)
  },

  /**
   * 获取花样搜索选项（统一接口）
   */
  getSearchOptions(): Promise<PatternSearchOptions> {
    console.log('🔍 patternApi.getSearchOptions() 被调用')
    console.log('🔍 请求URL: /patterns/search-options')
    return http.get('/patterns/search-options')
  },

  /**
   * 获取文件类型选项（已废弃，请使用getSearchOptions）
   * @deprecated 请使用getSearchOptions()获取统一的搜索选项
   */
  getFileTypes(): Promise<Array<{ label: string; value: string }>> {
    return http.get('/patterns/file-types')
  },

  /**
   * 获取创建人选项（已废弃，请使用getSearchOptions）
   * @deprecated 请使用getSearchOptions()获取统一的搜索选项
   */
  getCreateUsers(): Promise<Array<{ label: string; value: string }>> {
    return http.get('/patterns/create-users')
  },

  /**
   * 获取花样统计信息
   */
  getPatternStats(): Promise<PatternStats> {
    return http.get('/patterns/stats')
  }
}
