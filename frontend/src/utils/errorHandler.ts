/**
 * 前端错误处理工具
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:30:00 +08:00; Reason: Task-009 添加数据验证和错误处理, 统一前端错误处理逻辑; Principle_Applied: 错误处理;}}
 */

import { message } from 'ant-design-vue'

// 错误类型枚举
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType
  message: string
  details?: string
  statusCode?: number
}

// HTTP状态码到错误类型的映射
const STATUS_CODE_MAP: Record<number, ErrorType> = {
  400: ErrorType.VALIDATION_ERROR,
  401: ErrorType.PERMISSION_ERROR,
  403: ErrorType.PERMISSION_ERROR,
  404: ErrorType.NOT_FOUND_ERROR,
  409: ErrorType.CONFLICT_ERROR,
  422: ErrorType.VALIDATION_ERROR,
  500: ErrorType.SERVER_ERROR,
  502: ErrorType.SERVER_ERROR,
  503: ErrorType.SERVER_ERROR,
  504: ErrorType.SERVER_ERROR
}

// 默认错误消息
const DEFAULT_ERROR_MESSAGES: Record<ErrorType, string> = {
  [ErrorType.VALIDATION_ERROR]: '数据验证失败，请检查输入内容',
  [ErrorType.PERMISSION_ERROR]: '没有权限执行此操作',
  [ErrorType.NOT_FOUND_ERROR]: '请求的资源不存在',
  [ErrorType.CONFLICT_ERROR]: '操作冲突，请刷新页面后重试',
  [ErrorType.SERVER_ERROR]: '服务器内部错误，请稍后重试',
  [ErrorType.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ErrorType.UNKNOWN_ERROR]: '未知错误，请稍后重试'
}

// 特定业务错误消息映射
const BUSINESS_ERROR_MESSAGES: Record<string, string> = {
  // 工价配置相关
  '该花样已存在工价配置': '该花样已配置工价，请勿重复创建',
  '企业已存在换片单价配置': '企业已存在换片单价配置，请勿重复创建',
  '企业已存在换线单价配置': '企业已存在换线单价配置，请勿重复创建',
  '指定的花样不存在': '选择的花样不存在或已被删除',
  '该工价配置已被使用': '该工价配置正在使用中，无法删除',
  
  // 权限相关
  '没有权限访问': '您没有权限访问此功能',
  '登录已过期': '登录已过期，请重新登录',
  '账号已被禁用': '您的账号已被禁用，请联系管理员',
  
  // 数据验证相关
  '累计针数必须大于0': '累计针数必须是大于0的整数',
  '奖励金额不能为负数': '奖励金额不能为负数',
  '累计针数不能重复': '激励工价规则中的累计针数不能重复',
  '激励工价规则最多不能超过20条': '激励工价规则最多只能设置20条'
}

/**
 * 解析错误信息
 * @param error 错误对象
 * @returns 解析后的错误信息
 */
export function parseError(error: any): ErrorInfo {
  // 网络错误
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    return {
      type: ErrorType.NETWORK_ERROR,
      message: DEFAULT_ERROR_MESSAGES[ErrorType.NETWORK_ERROR],
      statusCode: 0
    }
  }

  const response = error.response
  const statusCode = response.status
  const responseData = response.data || {}

  // 根据状态码确定错误类型
  const errorType = STATUS_CODE_MAP[statusCode] || ErrorType.UNKNOWN_ERROR

  // 提取错误消息
  let errorMessage = responseData.error || responseData.message || error.message

  // 检查是否有业务特定的错误消息
  if (errorMessage && BUSINESS_ERROR_MESSAGES[errorMessage]) {
    errorMessage = BUSINESS_ERROR_MESSAGES[errorMessage]
  }

  // 如果没有具体错误消息，使用默认消息
  if (!errorMessage) {
    errorMessage = DEFAULT_ERROR_MESSAGES[errorType]
  }

  return {
    type: errorType,
    message: errorMessage,
    details: responseData.details,
    statusCode
  }
}

/**
 * 显示错误消息
 * @param error 错误对象
 * @param customMessage 自定义消息（可选）
 */
export function showError(error: any, customMessage?: string): void {
  const errorInfo = parseError(error)
  const displayMessage = customMessage || errorInfo.message

  switch (errorInfo.type) {
    case ErrorType.VALIDATION_ERROR:
      message.error(displayMessage)
      break
    case ErrorType.PERMISSION_ERROR:
      message.warning(displayMessage)
      break
    case ErrorType.NOT_FOUND_ERROR:
      message.warning(displayMessage)
      break
    case ErrorType.CONFLICT_ERROR:
      message.warning(displayMessage)
      break
    case ErrorType.SERVER_ERROR:
      message.error(displayMessage)
      break
    case ErrorType.NETWORK_ERROR:
      message.error(displayMessage)
      break
    default:
      message.error(displayMessage)
  }
}

/**
 * 处理API调用错误
 * @param error 错误对象
 * @param operation 操作名称
 * @param showMessage 是否显示错误消息（默认true）
 * @returns 错误信息
 */
export function handleApiError(
  error: any, 
  operation: string, 
  showMessage: boolean = true
): ErrorInfo {
  const errorInfo = parseError(error)
  
  // 记录错误日志
  console.error(`${operation}失败:`, {
    type: errorInfo.type,
    message: errorInfo.message,
    statusCode: errorInfo.statusCode,
    details: errorInfo.details,
    originalError: error
  })

  // 显示错误消息
  if (showMessage) {
    showError(error)
  }

  return errorInfo
}

/**
 * 表单验证错误处理
 * @param validationErrors 验证错误数组
 */
export function handleValidationErrors(validationErrors: any[]): void {
  if (!validationErrors || validationErrors.length === 0) {
    return
  }

  const errorMessages = validationErrors.map(error => {
    if (typeof error === 'string') {
      return error
    }
    return error.message || error.msg || '验证失败'
  })

  const displayMessage = errorMessages.length === 1 
    ? errorMessages[0] 
    : `数据验证失败：${errorMessages.join('；')}`

  message.error(displayMessage)
}

/**
 * 异步操作错误处理装饰器
 * @param operation 操作名称
 * @param showLoading 是否显示加载状态
 */
export function withErrorHandling(operation: string, showLoading: boolean = false) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      let loadingMessage: any = null
      
      try {
        if (showLoading) {
          loadingMessage = message.loading(`${operation}中...`, 0)
        }

        const result = await originalMethod.apply(this, args)
        
        if (loadingMessage) {
          loadingMessage()
        }

        return result
      } catch (error) {
        if (loadingMessage) {
          loadingMessage()
        }

        handleApiError(error, operation)
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 创建错误处理的API调用包装器
 * @param apiCall API调用函数
 * @param operation 操作名称
 * @returns 包装后的API调用函数
 */
export function createApiWrapper<T extends (...args: any[]) => Promise<any>>(
  apiCall: T,
  operation: string
): T {
  return (async (...args: any[]) => {
    try {
      return await apiCall(...args)
    } catch (error) {
      handleApiError(error, operation)
      throw error
    }
  }) as T
}

/**
 * 批量操作错误处理
 * @param operations 操作数组
 * @param operationName 操作名称
 * @returns 成功和失败的结果
 */
export async function handleBatchOperations<T>(
  operations: (() => Promise<T>)[],
  operationName: string
): Promise<{
  successes: T[]
  failures: ErrorInfo[]
  total: number
}> {
  const successes: T[] = []
  const failures: ErrorInfo[] = []

  for (let i = 0; i < operations.length; i++) {
    try {
      const result = await operations[i]()
      successes.push(result)
    } catch (error) {
      const errorInfo = handleApiError(error, `${operationName}(${i + 1})`, false)
      failures.push(errorInfo)
    }
  }

  // 显示批量操作结果
  if (failures.length === 0) {
    message.success(`${operationName}全部成功，共处理${operations.length}项`)
  } else if (successes.length === 0) {
    message.error(`${operationName}全部失败，共${failures.length}项`)
  } else {
    message.warning(`${operationName}部分成功：成功${successes.length}项，失败${failures.length}项`)
  }

  return {
    successes,
    failures,
    total: operations.length
  }
}
