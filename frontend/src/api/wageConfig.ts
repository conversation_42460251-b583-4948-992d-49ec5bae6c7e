/**
 * 工价配置API接口
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 16:35:00 +08:00; Reason: Task-006 创建前端WageConfig API接口, 定义TypeScript接口和HTTP请求方法; Principle_Applied: 前端API封装;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 19:20:00 +08:00; Reason: 重构为使用类型导入, 解决模块导出问题; Principle_Applied: 类型安全;}}
 */

import { http } from './request'
import type {
  WageConfig,
  WageConfigListQuery,
  WageConfigListResponse,
  WageConfigSearchOptions,
  CreateWageConfigRequest,
  UpdateWageConfigRequest
} from '../types/wageConfig'

/**
 * 工价配置管理API
 */
export const wageConfigApi = {
  /**
   * 获取工价配置列表
   * @param params 查询参数
   * @returns 工价配置列表响应
   */
  getWageConfigList(params: WageConfigListQuery): Promise<WageConfigListResponse> {
    return http.get('/wage-configs', { params })
  },

  /**
   * 获取工价配置详情
   * @param id 工价配置ID
   * @returns 工价配置详情
   */
  getWageConfigById(id: number): Promise<WageConfig> {
    return http.get(`/wage-configs/${id}`)
  },

  /**
   * 创建工价配置
   * @param data 创建请求数据
   * @returns 创建的工价配置
   */
  createWageConfig(data: CreateWageConfigRequest): Promise<WageConfig> {
    return http.post('/wage-configs', data)
  },

  /**
   * 更新工价配置
   * @param id 工价配置ID
   * @param data 更新请求数据
   * @returns 更新后的工价配置
   */
  updateWageConfig(id: number, data: UpdateWageConfigRequest): Promise<WageConfig> {
    return http.put(`/wage-configs/${id}`, data)
  },

  /**
   * 删除工价配置
   * @param id 工价配置ID
   * @returns 删除结果
   */
  deleteWageConfig(id: number): Promise<void> {
    return http.delete(`/wage-configs/${id}`)
  },

  /**
   * 获取工价配置搜索选项
   * @returns 搜索选项数据
   */
  getSearchOptions(): Promise<WageConfigSearchOptions> {
    return http.get('/wage-configs/search-options')
  }
}

// 重新导出类型和常量
export {
  WageTypeLabels,
  WageTypeOptions,
  formatIncentiveWage,
  validateIncentiveWage,
  createDefaultIncentiveWageItem
} from '../types/wageConfig'
export type {
  WageConfig,
  WageConfigListQuery,
  WageConfigListResponse,
  WageConfigSearchOptions,
  CreateWageConfigRequest,
  UpdateWageConfigRequest,
  IncentiveWageItem,
  WageType
} from '../types/wageConfig'
