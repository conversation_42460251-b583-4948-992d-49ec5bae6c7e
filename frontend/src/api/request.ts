/**
 * HTTP请求封装
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建HTTP请求封装; Principle_Applied: 网络请求统一管理;}}
 */

import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import type { ApiResponse } from '@/types/auth'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    
    // 添加认证令牌
    if (userStore.accessToken) {
      config.headers.Authorization = `Bearer ${userStore.accessToken}`
    }
    
    // 添加请求ID（用于日志追踪）
    config.headers['X-Request-ID'] = generateRequestId()
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 检查业务状态码
    if (data.code === 200 || data.code === 201) {
      return response
    } else {
      // 业务错误 - 不在这里显示错误消息，让错误拦截器统一处理
      const errorMessage = data.message || '请求失败'
      const error = new Error(errorMessage)
      // 添加响应数据到错误对象，供错误拦截器使用
      ;(error as any).response = response
      return Promise.reject(error)
    }
  },
  async (error) => {
    const userStore = useUserStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未认证或令牌过期
          if (data.code === 'AUTH_FAILED' || data.message?.includes('过期')) {
            // 尝试刷新令牌
            const refreshed = await userStore.refreshAccessToken()
            if (refreshed && error.config) {
              // 重新发送原请求
              error.config.headers.Authorization = `Bearer ${userStore.accessToken}`
              return request(error.config)
            } else {
              // 刷新失败，跳转到登录页
              message.error('登录已过期，请重新登录')
              await userStore.logout()
              window.location.href = '/login'
            }
          } else {
            message.error(data.message || '认证失败')
          }
          break
          
        case 403:
          // 权限不足
          message.error(data.message || '权限不足')
          break
          
        case 404:
          // 资源不存在
          message.error(data.message || '请求的资源不存在')
          break
          
        case 429:
          // 请求过于频繁
          message.error(data.message || '请求过于频繁，请稍后再试')
          break
          
        case 500:
          // 服务器错误
          message.error(data.message || '服务器内部错误')
          break
          
        default:
          message.error(data.message || `请求失败 (${status})`)
      }
    } else if (error.request) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      message.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 封装常用的HTTP方法
export const http = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return request.get<ApiResponse<T>>(url, config).then(res => res.data.data)
  },
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.post<ApiResponse<T>>(url, data, config).then(res => res.data.data)
  },
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.put<ApiResponse<T>>(url, data, config).then(res => res.data.data)
  },
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return request.delete<ApiResponse<T>>(url, config).then(res => res.data.data)
  },
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.patch<ApiResponse<T>>(url, data, config).then(res => res.data.data)
  }
}

export default request
