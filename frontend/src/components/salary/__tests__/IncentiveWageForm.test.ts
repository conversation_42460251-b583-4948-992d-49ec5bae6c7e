/**
 * IncentiveWageForm组件测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 18:00:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建激励工价配置组件测试; Principle_Applied: 组件测试;}}
 */

import { mount, VueWrapper } from '@vue/test-utils'
import { message } from 'ant-design-vue'
import IncentiveWageForm from '../IncentiveWageForm.vue'
import type { IncentiveWageItem } from '../../../api/wageConfig'

// Mock dependencies
jest.mock('ant-design-vue', () => ({
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn()
  }
}))

describe('IncentiveWageForm', () => {
  let wrapper: VueWrapper<any>

  const mockIncentiveWage: IncentiveWageItem[] = [
    { cumulative: 10000, reward: 50 },
    { cumulative: 50000, reward: 200 },
    { cumulative: 100000, reward: 500 }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    
    wrapper = mount(IncentiveWageForm, {
      props: {
        modelValue: mockIncentiveWage,
        disabled: false,
        maxRules: 10
      },
      global: {
        stubs: {
          'a-button': true,
          'a-dropdown': true,
          'a-menu': true,
          'a-menu-item': true,
          'a-menu-divider': true,
          'a-input-number': true,
          'a-empty': true,
          'a-space': true,
          'a-modal': true,
          'a-textarea': true
        }
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('组件初始化', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.incentive-wage-form').exists()).toBe(true)
    })

    it('应该正确初始化props', () => {
      expect(wrapper.vm.localRules).toEqual(mockIncentiveWage)
      expect(wrapper.vm.disabled).toBe(false)
      expect(wrapper.vm.maxRules).toBe(10)
    })

    it('应该显示规则列表', () => {
      expect(wrapper.vm.hasRules).toBe(true)
      expect(wrapper.vm.localRules.length).toBe(3)
    })
  })

  describe('计算属性', () => {
    it('应该正确计算最高奖励', () => {
      expect(wrapper.vm.maxReward).toBe(500)
    })

    it('应该正确计算总奖励', () => {
      expect(wrapper.vm.totalReward).toBe(750)
    })

    it('应该正确判断是否有规则', () => {
      expect(wrapper.vm.hasRules).toBe(true)
      
      // 测试空规则
      wrapper.setProps({ modelValue: [] })
      expect(wrapper.vm.hasRules).toBe(false)
    })
  })

  describe('添加规则', () => {
    it('应该成功添加新规则', async () => {
      const initialLength = wrapper.vm.localRules.length
      
      await wrapper.vm.addRule()

      expect(wrapper.vm.localRules.length).toBe(initialLength + 1)
      expect(wrapper.vm.localRules[initialLength]).toEqual({
        cumulative: 0,
        reward: 0
      })
      expect(message.success).toHaveBeenCalledWith('添加规则成功')
    })

    it('应该限制最大规则数量', async () => {
      // 设置已达到最大规则数
      wrapper.setProps({ maxRules: 3 })
      
      await wrapper.vm.addRule()

      expect(wrapper.vm.localRules.length).toBe(3)
      expect(message.warning).toHaveBeenCalledWith('最多只能添加3条规则')
    })
  })

  describe('删除规则', () => {
    it('应该成功删除指定规则', async () => {
      const initialLength = wrapper.vm.localRules.length
      
      await wrapper.vm.removeRule(1)

      expect(wrapper.vm.localRules.length).toBe(initialLength - 1)
      expect(wrapper.vm.localRules[1]).toEqual({ cumulative: 100000, reward: 500 })
      expect(message.success).toHaveBeenCalledWith('删除规则成功')
    })

    it('应该正确重新索引错误信息', async () => {
      wrapper.vm.ruleErrors = {
        0: '错误1',
        1: '错误2',
        2: '错误3'
      }

      await wrapper.vm.removeRule(1)

      expect(wrapper.vm.ruleErrors).toEqual({
        0: '错误1',
        1: '错误3'
      })
    })
  })

  describe('规则验证', () => {
    it('应该验证有效的规则', () => {
      const validRule = { cumulative: 10000, reward: 50 }
      wrapper.vm.localRules = [validRule]

      const result = wrapper.vm.validateRule(0)

      expect(result).toBe(true)
      expect(wrapper.vm.ruleErrors[0]).toBeUndefined()
    })

    it('应该拒绝无效的累计针数', () => {
      const invalidRule = { cumulative: 0, reward: 50 }
      wrapper.vm.localRules = [invalidRule]

      const result = wrapper.vm.validateRule(0)

      expect(result).toBe(false)
      expect(wrapper.vm.ruleErrors[0]).toBe('累计针数必须大于0')
    })

    it('应该拒绝负数奖励金额', () => {
      const invalidRule = { cumulative: 10000, reward: -10 }
      wrapper.vm.localRules = [invalidRule]

      const result = wrapper.vm.validateRule(0)

      expect(result).toBe(false)
      expect(wrapper.vm.ruleErrors[0]).toBe('奖励金额不能为负数')
    })

    it('应该检测重复的累计针数', () => {
      wrapper.vm.localRules = [
        { cumulative: 10000, reward: 50 },
        { cumulative: 10000, reward: 100 }
      ]

      const result = wrapper.vm.validateRule(1)

      expect(result).toBe(false)
      expect(wrapper.vm.ruleErrors[1]).toBe('累计针数不能重复')
    })

    it('应该验证所有规则', () => {
      wrapper.vm.localRules = [
        { cumulative: 10000, reward: 50 },
        { cumulative: 0, reward: 100 }, // 无效
        { cumulative: 50000, reward: 200 }
      ]

      const result = wrapper.vm.validateAllRules()

      expect(result).toBe(false)
      expect(wrapper.vm.ruleErrors[1]).toBeDefined()
    })
  })

  describe('规则排序', () => {
    it('应该按累计针数排序', () => {
      wrapper.vm.localRules = [
        { cumulative: 50000, reward: 200 },
        { cumulative: 10000, reward: 50 },
        { cumulative: 100000, reward: 500 }
      ]

      wrapper.vm.sortRules()

      expect(wrapper.vm.localRules[0].cumulative).toBe(10000)
      expect(wrapper.vm.localRules[1].cumulative).toBe(50000)
      expect(wrapper.vm.localRules[2].cumulative).toBe(100000)
    })
  })

  describe('模板功能', () => {
    it('应该显示模板选择模态框', async () => {
      await wrapper.vm.showTemplateModal()

      expect(wrapper.vm.templateModalVisible).toBe(true)
      expect(wrapper.vm.selectedTemplate).toBe('')
    })

    it('应该应用选中的模板', async () => {
      wrapper.vm.selectedTemplate = 'basic'
      wrapper.vm.templateModalVisible = true

      await wrapper.vm.applyTemplate()

      expect(wrapper.vm.localRules.length).toBeGreaterThan(0)
      expect(wrapper.vm.templateModalVisible).toBe(false)
      expect(message.success).toHaveBeenCalledWith('已应用模板：基础激励')
    })

    it('应该处理未选择模板的情况', async () => {
      wrapper.vm.selectedTemplate = ''

      await wrapper.vm.applyTemplate()

      expect(message.error).toHaveBeenCalledWith('请选择一个模板')
    })
  })

  describe('导入导出功能', () => {
    it('应该显示导入模态框', async () => {
      await wrapper.vm.showImportModal()

      expect(wrapper.vm.importModalVisible).toBe(true)
      expect(wrapper.vm.importText).toBe('')
    })

    it('应该成功导入规则', async () => {
      wrapper.vm.importText = '10000,50\n50000,200\n100000,500'

      await wrapper.vm.importRules()

      expect(wrapper.vm.localRules).toEqual([
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 },
        { cumulative: 100000, reward: 500 }
      ])
      expect(wrapper.vm.importModalVisible).toBe(false)
      expect(message.success).toHaveBeenCalledWith('成功导入3条规则')
    })

    it('应该处理导入格式错误', async () => {
      wrapper.vm.importText = '10000\n50000,200'

      await wrapper.vm.importRules()

      expect(message.error).toHaveBeenCalledWith('第1行格式错误，应为：累计针数,奖励金额')
    })

    it('应该处理导入数据验证错误', async () => {
      wrapper.vm.importText = '0,50\n50000,200'

      await wrapper.vm.importRules()

      expect(message.error).toHaveBeenCalledWith('第1行累计针数无效')
    })

    it('应该检测导入数据重复', async () => {
      wrapper.vm.importText = '10000,50\n10000,100'

      await wrapper.vm.importRules()

      expect(message.error).toHaveBeenCalledWith('累计针数10000重复')
    })

    it('应该导出规则', async () => {
      // Mock DOM API
      const mockLink = {
        href: '',
        download: '',
        click: jest.fn()
      }
      const mockCreateElement = jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any)
      const mockAppendChild = jest.spyOn(document.body, 'appendChild').mockImplementation()
      const mockRemoveChild = jest.spyOn(document.body, 'removeChild').mockImplementation()
      const mockCreateObjectURL = jest.spyOn(URL, 'createObjectURL').mockReturnValue('blob:url')
      const mockRevokeObjectURL = jest.spyOn(URL, 'revokeObjectURL').mockImplementation()

      await wrapper.vm.exportRules()

      expect(mockCreateElement).toHaveBeenCalledWith('a')
      expect(mockLink.click).toHaveBeenCalled()
      expect(message.success).toHaveBeenCalledWith('导出成功')

      // Restore mocks
      mockCreateElement.mockRestore()
      mockAppendChild.mockRestore()
      mockRemoveChild.mockRestore()
      mockCreateObjectURL.mockRestore()
      mockRevokeObjectURL.mockRestore()
    })

    it('应该处理空规则导出', async () => {
      wrapper.setProps({ modelValue: [] })

      await wrapper.vm.exportRules()

      expect(message.warning).toHaveBeenCalledWith('没有可导出的规则')
    })
  })

  describe('清空功能', () => {
    it('应该清空所有规则', async () => {
      await wrapper.vm.clearAllRules()

      expect(wrapper.vm.localRules).toEqual([])
      expect(wrapper.vm.ruleErrors).toEqual({})
      expect(message.success).toHaveBeenCalledWith('已清空所有规则')
    })
  })

  describe('事件发射', () => {
    it('应该发射update:modelValue事件', async () => {
      await wrapper.vm.addRule()

      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('change')).toBeTruthy()
    })

    it('应该发射validate事件', async () => {
      wrapper.vm.localRules = [
        { cumulative: 0, reward: 50 } // 无效规则
      ]

      wrapper.vm.validateAllRules()

      expect(wrapper.emitted('validate')).toBeTruthy()
      const validateEvent = wrapper.emitted('validate')![0]
      expect(validateEvent[0]).toBe(false) // isValid
      expect(validateEvent[1]).toEqual(['第1条规则：累计针数必须大于0']) // errors
    })
  })

  describe('禁用状态', () => {
    beforeEach(() => {
      wrapper.setProps({ disabled: true })
    })

    it('应该禁用添加按钮', () => {
      expect(wrapper.vm.disabled).toBe(true)
    })

    it('应该禁用输入框', () => {
      // 在实际实现中，输入框应该根据disabled prop禁用
      expect(wrapper.vm.disabled).toBe(true)
    })
  })

  describe('响应式更新', () => {
    it('应该响应modelValue变化', async () => {
      const newValue = [
        { cumulative: 20000, reward: 100 }
      ]

      await wrapper.setProps({ modelValue: newValue })

      expect(wrapper.vm.localRules).toEqual(newValue)
    })

    it('应该清除错误信息', async () => {
      wrapper.vm.ruleErrors = { 0: '错误信息' }

      await wrapper.setProps({ modelValue: [] })

      expect(wrapper.vm.ruleErrors).toEqual({})
    })
  })
})
