/**
 * 工价配置路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 16:25:00 +08:00; Reason: Task-005 创建WageConfig路由配置, 集成权限中间件; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { wageConfigController } from './wageConfig.controller';
import { authMiddleware, permissionMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     WageConfig:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 工价配置ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         createdUserId:
 *           type: integer
 *           description: 创建人ID
 *         updatedUserId:
 *           type: integer
 *           description: 修改人ID
 *         patternId:
 *           type: integer
 *           description: 花样ID（可选）
 *         patternWage:
 *           type: number
 *           description: 花样工价（元/万针）
 *         incentiveWage:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               cumulative:
 *                 type: integer
 *                 description: 累计针数
 *               reward:
 *                 type: number
 *                 description: 奖励金额
 *           description: 激励工价JSON
 *         type:
 *           type: integer
 *           enum: [1, 2, 3]
 *           description: 工价类型（1-花样工价，2-换片单价，3-换线单价）
 *         changePieceWage:
 *           type: number
 *           description: 换片单价
 *         changeLineWage:
 *           type: number
 *           description: 换线单价
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/v1/wage-configs:
 *   get:
 *     summary: 获取工价配置列表
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词（花样名称、创建人姓名）
 *       - in: query
 *         name: type
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: 工价类型
 *       - in: query
 *         name: patternId
 *         schema:
 *           type: integer
 *         description: 花样ID
 *       - in: query
 *         name: createdUserId
 *         schema:
 *           type: integer
 *         description: 创建人ID
 *     responses:
 *       200:
 *         description: 获取工价配置列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取工价配置列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     wageConfigs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/WageConfig'
 *                     total:
 *                       type: integer
 *                       description: 总数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页数量
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 */
router.get('/', authMiddleware, permissionMiddleware(['salary_rate:view']), asyncHandler(wageConfigController.getWageConfigList));

/**
 * @swagger
 * /api/v1/wage-configs/search-options:
 *   get:
 *     summary: 获取工价配置搜索选项
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取工价配置搜索选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取工价配置搜索选项成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     patterns:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 花样ID
 *                           name:
 *                             type: string
 *                             description: 花样名称
 *                           code:
 *                             type: string
 *                             description: 花样编号
 *                     createdUsers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: 用户ID
 *                           name:
 *                             type: string
 *                             description: 用户姓名
 *                     types:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           label:
 *                             type: string
 *                             description: 类型标签
 *                           value:
 *                             type: integer
 *                             description: 类型值
 */
router.get('/search-options', authMiddleware, permissionMiddleware(['salary_rate:view']), asyncHandler(wageConfigController.getSearchOptions));

/**
 * @swagger
 * /api/v1/wage-configs/{id}:
 *   get:
 *     summary: 获取工价配置详情
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工价配置ID
 *     responses:
 *       200:
 *         description: 获取工价配置详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取工价配置详情成功
 *                 data:
 *                   $ref: '#/components/schemas/WageConfig'
 *       404:
 *         description: 工价配置不存在
 */
router.get('/:id', authMiddleware, permissionMiddleware(['salary_rate:view']), asyncHandler(wageConfigController.getWageConfigById));

/**
 * @swagger
 * /api/v1/wage-configs:
 *   post:
 *     summary: 创建工价配置
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *             properties:
 *               type:
 *                 type: integer
 *                 enum: [1, 2, 3]
 *                 description: 工价类型（1-花样工价，2-换片单价，3-换线单价）
 *               patternId:
 *                 type: integer
 *                 description: 花样ID（花样工价类型必填）
 *               patternWage:
 *                 type: number
 *                 description: 花样工价（元/万针）
 *               incentiveWage:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     cumulative:
 *                       type: integer
 *                       description: 累计针数
 *                     reward:
 *                       type: number
 *                       description: 奖励金额
 *                 description: 激励工价JSON
 *               changePieceWage:
 *                 type: number
 *                 description: 换片单价
 *               changeLineWage:
 *                 type: number
 *                 description: 换线单价
 *     responses:
 *       201:
 *         description: 创建工价配置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建工价配置成功
 *                 data:
 *                   $ref: '#/components/schemas/WageConfig'
 *       400:
 *         description: 请求参数错误
 */
router.post('/', authMiddleware, permissionMiddleware(['salary_rate:create']), asyncHandler(wageConfigController.createWageConfig));

/**
 * @swagger
 * /api/v1/wage-configs/{id}:
 *   put:
 *     summary: 更新工价配置
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工价配置ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: integer
 *                 enum: [1, 2, 3]
 *                 description: 工价类型（1-花样工价，2-换片单价，3-换线单价）
 *               patternId:
 *                 type: integer
 *                 description: 花样ID
 *               patternWage:
 *                 type: number
 *                 description: 花样工价（元/万针）
 *               incentiveWage:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     cumulative:
 *                       type: integer
 *                       description: 累计针数
 *                     reward:
 *                       type: number
 *                       description: 奖励金额
 *                 description: 激励工价JSON
 *               changePieceWage:
 *                 type: number
 *                 description: 换片单价
 *               changeLineWage:
 *                 type: number
 *                 description: 换线单价
 *     responses:
 *       200:
 *         description: 更新工价配置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新工价配置成功
 *                 data:
 *                   $ref: '#/components/schemas/WageConfig'
 *       404:
 *         description: 工价配置不存在
 */
router.put('/:id', authMiddleware, permissionMiddleware(['salary_rate:update']), asyncHandler(wageConfigController.updateWageConfig));

/**
 * @swagger
 * /api/v1/wage-configs/{id}:
 *   delete:
 *     summary: 删除工价配置
 *     tags: [工价管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工价配置ID
 *     responses:
 *       200:
 *         description: 删除工价配置成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除工价配置成功
 *       404:
 *         description: 工价配置不存在
 */
router.delete('/:id', authMiddleware, permissionMiddleware(['salary_rate:delete']), asyncHandler(wageConfigController.deleteWageConfig));

export default router;
