/**
 * SalaryRatesView组件测试
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 17:55:00 +08:00; Reason: Task-010 编写单元测试和集成测试, 创建前端页面组件测试; Principle_Applied: 组件测试;}}
 */

import { mount, VueWrapper } from '@vue/test-utils'
import { message } from 'ant-design-vue'
import SalaryRatesView from '../SalaryRatesView.vue'
import { wageConfigApi } from '../../../api/wageConfig'
import { WageType } from '../../../api/wageConfig'

// Mock dependencies
jest.mock('../../../api/wageConfig')
jest.mock('ant-design-vue', () => ({
  message: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn()
  }
}))

const mockWageConfigApi = wageConfigApi as jest.Mocked<typeof wageConfigApi>

describe('SalaryRatesView', () => {
  let wrapper: VueWrapper<any>

  const mockWageConfigs = [
    {
      id: 1,
      type: WageType.PATTERN_WAGE,
      patternId: 1,
      patternWage: 100.50,
      incentiveWage: [
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 }
      ],
      pattern: {
        id: 1,
        name: '测试花样',
        code: 'TEST001'
      },
      createdUser: {
        id: 1,
        name: '测试用户'
      },
      createdAt: '2025-07-27T10:00:00Z'
    },
    {
      id: 2,
      type: WageType.CHANGE_PIECE_WAGE,
      changePieceWage: 5.00,
      createdUser: {
        id: 1,
        name: '测试用户'
      },
      createdAt: '2025-07-27T11:00:00Z'
    }
  ]

  const mockListResponse = {
    wageConfigs: mockWageConfigs,
    total: 2,
    page: 1,
    pageSize: 10,
    totalPages: 1
  }

  const mockSearchOptions = {
    patterns: [
      { id: 1, name: '测试花样', code: 'TEST001' }
    ],
    createdUsers: [
      { id: 1, name: '测试用户' }
    ],
    types: [
      { label: '花样工价', value: 1 },
      { label: '换片单价', value: 2 },
      { label: '换线单价', value: 3 }
    ]
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default API mocks
    mockWageConfigApi.getWageConfigList.mockResolvedValue(mockListResponse)
    mockWageConfigApi.getSearchOptions.mockResolvedValue(mockSearchOptions)
    mockWageConfigApi.getWageConfigById.mockResolvedValue(mockWageConfigs[0])
    mockWageConfigApi.createWageConfig.mockResolvedValue(mockWageConfigs[0])
    mockWageConfigApi.updateWageConfig.mockResolvedValue(mockWageConfigs[0])
    mockWageConfigApi.deleteWageConfig.mockResolvedValue()

    wrapper = mount(SalaryRatesView, {
      global: {
        stubs: {
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-input': true,
          'a-select': true,
          'a-select-option': true,
          'a-button': true,
          'a-space': true,
          'a-table': true,
          'a-tag': true,
          'a-modal': true,
          'a-form': true,
          'a-form-item': true,
          'a-radio-group': true,
          'a-radio': true,
          'a-input-number': true,
          'a-empty': true,
          'IncentiveWageForm': true
        }
      }
    })
  })

  afterEach(() => {
    wrapper.unmount()
  })

  describe('组件初始化', () => {
    it('应该正确渲染组件', () => {
      expect(wrapper.exists()).toBe(true)
      expect(wrapper.find('.salary-rates').exists()).toBe(true)
    })

    it('应该在挂载时加载数据', () => {
      expect(mockWageConfigApi.getWageConfigList).toHaveBeenCalled()
      expect(mockWageConfigApi.getSearchOptions).toHaveBeenCalled()
    })

    it('应该初始化搜索表单', () => {
      const vm = wrapper.vm
      expect(vm.searchForm.search).toBe('')
      expect(vm.searchForm.type).toBeUndefined()
      expect(vm.searchForm.patternId).toBeUndefined()
      expect(vm.searchForm.createdUserId).toBeUndefined()
    })
  })

  describe('数据加载', () => {
    it('应该正确加载工价配置列表', async () => {
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.dataList).toEqual(mockWageConfigs)
      expect(wrapper.vm.pagination.total).toBe(2)
    })

    it('应该处理加载错误', async () => {
      const error = new Error('网络错误')
      mockWageConfigApi.getWageConfigList.mockRejectedValue(error)

      await wrapper.vm.loadData()

      expect(message.error).toHaveBeenCalled()
      expect(wrapper.vm.dataList).toEqual([])
      expect(wrapper.vm.pagination.total).toBe(0)
    })

    it('应该正确加载搜索选项', async () => {
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.searchOptions).toEqual(mockSearchOptions)
    })
  })

  describe('搜索功能', () => {
    it('应该支持关键词搜索', async () => {
      wrapper.vm.searchForm.search = '测试'
      await wrapper.vm.handleSearch()

      expect(mockWageConfigApi.getWageConfigList).toHaveBeenCalledWith(
        expect.objectContaining({
          search: '测试'
        })
      )
    })

    it('应该支持工价类型筛选', async () => {
      wrapper.vm.searchForm.type = WageType.PATTERN_WAGE
      await wrapper.vm.handleSearch()

      expect(mockWageConfigApi.getWageConfigList).toHaveBeenCalledWith(
        expect.objectContaining({
          type: WageType.PATTERN_WAGE
        })
      )
    })

    it('应该支持花样筛选', async () => {
      wrapper.vm.searchForm.patternId = 1
      await wrapper.vm.handleSearch()

      expect(mockWageConfigApi.getWageConfigList).toHaveBeenCalledWith(
        expect.objectContaining({
          patternId: 1
        })
      )
    })

    it('应该支持重置搜索', async () => {
      wrapper.vm.searchForm.search = '测试'
      wrapper.vm.searchForm.type = WageType.PATTERN_WAGE
      
      await wrapper.vm.handleReset()

      expect(wrapper.vm.searchForm.search).toBe('')
      expect(wrapper.vm.searchForm.type).toBeUndefined()
      expect(wrapper.vm.pagination.current).toBe(1)
    })
  })

  describe('分页功能', () => {
    it('应该支持分页切换', async () => {
      const pagination = { current: 2, pageSize: 10 }
      await wrapper.vm.handleTableChange(pagination)

      expect(wrapper.vm.pagination.current).toBe(2)
      expect(wrapper.vm.pagination.pageSize).toBe(10)
      expect(mockWageConfigApi.getWageConfigList).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2,
          pageSize: 10
        })
      )
    })
  })

  describe('创建工价配置', () => {
    it('应该显示创建模态框', async () => {
      await wrapper.vm.showCreateModal()

      expect(wrapper.vm.modalVisible).toBe(true)
      expect(wrapper.vm.modalMode).toBe('create')
      expect(wrapper.vm.formData.type).toBe(WageType.PATTERN_WAGE)
    })

    it('应该成功创建花样工价配置', async () => {
      wrapper.vm.modalMode = 'create'
      wrapper.vm.formData = {
        type: WageType.PATTERN_WAGE,
        patternId: 1,
        patternWage: 100.50,
        incentiveWage: [
          { cumulative: 10000, reward: 50 }
        ]
      }

      // Mock form validation
      wrapper.vm.$refs.formRef = {
        validate: jest.fn().mockResolvedValue(true)
      }

      await wrapper.vm.handleModalOk()

      expect(mockWageConfigApi.createWageConfig).toHaveBeenCalledWith(wrapper.vm.formData)
      expect(message.success).toHaveBeenCalledWith('创建工价配置成功')
      expect(wrapper.vm.modalVisible).toBe(false)
    })

    it('应该成功创建换片单价配置', async () => {
      wrapper.vm.modalMode = 'create'
      wrapper.vm.formData = {
        type: WageType.CHANGE_PIECE_WAGE,
        changePieceWage: 5.00
      }

      wrapper.vm.$refs.formRef = {
        validate: jest.fn().mockResolvedValue(true)
      }

      await wrapper.vm.handleModalOk()

      expect(mockWageConfigApi.createWageConfig).toHaveBeenCalledWith(wrapper.vm.formData)
      expect(message.success).toHaveBeenCalledWith('创建工价配置成功')
    })

    it('应该处理创建错误', async () => {
      const error = new Error('该花样已存在工价配置')
      mockWageConfigApi.createWageConfig.mockRejectedValue(error)

      wrapper.vm.modalMode = 'create'
      wrapper.vm.$refs.formRef = {
        validate: jest.fn().mockResolvedValue(true)
      }

      await wrapper.vm.handleModalOk()

      expect(message.error).toHaveBeenCalled()
      expect(wrapper.vm.modalLoading).toBe(false)
    })
  })

  describe('编辑工价配置', () => {
    it('应该显示编辑模态框', async () => {
      const record = mockWageConfigs[0]
      await wrapper.vm.showEditModal(record)

      expect(wrapper.vm.modalVisible).toBe(true)
      expect(wrapper.vm.modalMode).toBe('edit')
      expect(wrapper.vm.formData.id).toBe(record.id)
      expect(wrapper.vm.formData.type).toBe(record.type)
    })

    it('应该成功更新工价配置', async () => {
      wrapper.vm.modalMode = 'edit'
      wrapper.vm.formData = {
        id: 1,
        type: WageType.PATTERN_WAGE,
        patternWage: 150.00
      }

      wrapper.vm.$refs.formRef = {
        validate: jest.fn().mockResolvedValue(true)
      }

      await wrapper.vm.handleModalOk()

      expect(mockWageConfigApi.updateWageConfig).toHaveBeenCalledWith(1, wrapper.vm.formData)
      expect(message.success).toHaveBeenCalledWith('更新工价配置成功')
    })
  })

  describe('删除工价配置', () => {
    it('应该成功删除工价配置', async () => {
      const record = mockWageConfigs[0]
      
      // Mock Modal.confirm
      const mockConfirm = jest.fn((options) => {
        options.onOk()
      })
      ;(wrapper.vm as any).Modal = { confirm: mockConfirm }

      await wrapper.vm.handleDelete(record)

      expect(mockWageConfigApi.deleteWageConfig).toHaveBeenCalledWith(record.id)
      expect(message.success).toHaveBeenCalledWith('删除成功')
    })

    it('应该处理删除错误', async () => {
      const error = new Error('该工价配置正在使用中')
      mockWageConfigApi.deleteWageConfig.mockRejectedValue(error)

      const record = mockWageConfigs[0]
      const mockConfirm = jest.fn((options) => {
        options.onOk()
      })
      ;(wrapper.vm as any).Modal = { confirm: mockConfirm }

      await wrapper.vm.handleDelete(record)

      expect(message.error).toHaveBeenCalled()
    })
  })

  describe('工具函数', () => {
    it('应该正确格式化工价类型标签', () => {
      expect(wrapper.vm.getWageTypeLabel(WageType.PATTERN_WAGE)).toBe('花样工价')
      expect(wrapper.vm.getWageTypeLabel(WageType.CHANGE_PIECE_WAGE)).toBe('换片单价')
      expect(wrapper.vm.getWageTypeLabel(WageType.CHANGE_LINE_WAGE)).toBe('换线单价')
    })

    it('应该正确格式化工价类型颜色', () => {
      expect(wrapper.vm.getWageTypeColor(WageType.PATTERN_WAGE)).toBe('blue')
      expect(wrapper.vm.getWageTypeColor(WageType.CHANGE_PIECE_WAGE)).toBe('green')
      expect(wrapper.vm.getWageTypeColor(WageType.CHANGE_LINE_WAGE)).toBe('orange')
    })

    it('应该正确格式化激励工价显示', () => {
      const incentiveWage = [
        { cumulative: 10000, reward: 50 },
        { cumulative: 50000, reward: 200 }
      ]
      expect(wrapper.vm.formatIncentiveWageDisplay(incentiveWage)).toBe('2条规则')
      expect(wrapper.vm.formatIncentiveWageDisplay([])).toBe('无')
    })

    it('应该正确格式化日期时间', () => {
      const dateTime = '2025-07-27T10:00:00Z'
      const result = wrapper.vm.formatDateTime(dateTime)
      expect(result).toMatch(/\d{4}\/\d{1,2}\/\d{1,2}/)
    })
  })

  describe('表单验证', () => {
    it('应该验证工价类型变化', async () => {
      wrapper.vm.formData = {
        type: WageType.PATTERN_WAGE,
        patternId: 1,
        patternWage: 100
      }

      await wrapper.vm.handleTypeChange()

      expect(wrapper.vm.formData.patternId).toBeUndefined()
      expect(wrapper.vm.formData.patternWage).toBeUndefined()
      expect(wrapper.vm.formData.incentiveWage).toEqual([])
    })

    it('应该处理激励工价验证', () => {
      const isValid = true
      const errors: string[] = []

      wrapper.vm.handleIncentiveWageValidate(isValid, errors)

      // 验证函数应该正常执行，不抛出错误
      expect(true).toBe(true)
    })
  })
})
