/**
 * 工资记录模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 10:30:00 +08:00; Reason: 创建Wage数据模型, 对应em_wage表; Principle_Applied: Sequelize ORM模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 工资记录属性接口
export interface WageAttributes {
  id: number;
  enterpriseId: number;                    // 企业ID
  deviceId?: number;                       // 设备ID
  userCode?: number;                       // 员工编码
  loginTime?: Date;                        // 上机时间
  logoutTime?: Date;                       // 下机时间
  patternId?: number;                      // 花样ID
  patternStitch?: number;                  // 花样针数
  patternWage?: number;                    // 花样工价 xxx元/万针
  productiveWage?: number;                 // 产量工资 pattern_stitch * pattern_wage
  changePieceCount?: number;               // 换片次数
  changePieceWage?: number;                // 换片工资
  changeLineCount?: number;                // 换线次数
  changeLineWage?: number;                 // 换线工资
  fineAmount?: number;                     // 罚款金额
  subsidyAmount?: number;                  // 奖励金额
  totalWage?: number;                      // 总工资
  latheCount?: number;                     // 车数
  remark?: string;                         // 备注
  createdAt: Date;
  updatedAt: Date;
}

// 创建工资记录时的可选属性
export type WageCreationAttributes = Optional<WageAttributes, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * 工资记录模型类
 */
export class Wage extends Model<WageAttributes, WageCreationAttributes> implements WageAttributes {
  public id!: number;
  public enterpriseId!: number;
  public deviceId?: number;
  public userCode?: number;
  public loginTime?: Date;
  public logoutTime?: Date;
  public patternId?: number;
  public patternStitch?: number;
  public patternWage?: number;
  public productiveWage?: number;
  public changePieceCount?: number;
  public changePieceWage?: number;
  public changeLineCount?: number;
  public changeLineWage?: number;
  public fineAmount?: number;
  public subsidyAmount?: number;
  public totalWage?: number;
  public latheCount?: number;
  public remark?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public enterprise?: any;
  public device?: any;
  public user?: any;
  public pattern?: any;
}

// 初始化工资记录模型
Wage.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '工资记录ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    deviceId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'device_id',
      comment: '设备ID',
      references: {
        model: 'em_device',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    userCode: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'user_code',
      comment: '员工编码',
    },
    loginTime: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'login_time',
      comment: '上机时间',
    },
    logoutTime: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'logout_time',
      comment: '下机时间',
    },
    patternId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'pattern_id',
      comment: '花样ID',
      references: {
        model: 'em_pattern',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    patternStitch: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'pattern_stitch',
      comment: '花样针数',
      validate: {
        min: {
          args: [0],
          msg: '花样针数不能为负数',
        },
      },
    },
    patternWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'pattern_wage',
      comment: '花样工价 xxx元/万针',
      validate: {
        min: {
          args: [0],
          msg: '花样工价不能为负数',
        },
      },
    },
    productiveWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'productive_wage',
      comment: '产量工资 pattern_stitch * pattern_wage',
      validate: {
        min: {
          args: [0],
          msg: '产量工资不能为负数',
        },
      },
    },
    changePieceCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'change_piece_count',
      comment: '换片次数',
      validate: {
        min: {
          args: [0],
          msg: '换片次数不能为负数',
        },
      },
    },
    changePieceWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'change_piece_wage',
      comment: '换片工资',
      validate: {
        min: {
          args: [0],
          msg: '换片工资不能为负数',
        },
      },
    },
    changeLineCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'change_line_count',
      comment: '换线次数',
      validate: {
        min: {
          args: [0],
          msg: '换线次数不能为负数',
        },
      },
    },
    changeLineWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'change_line_wage',
      comment: '换线工资',
      validate: {
        min: {
          args: [0],
          msg: '换线工资不能为负数',
        },
      },
    },
    fineAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'fine_amount',
      comment: '罚款金额',
      validate: {
        min: {
          args: [0],
          msg: '罚款金额不能为负数',
        },
      },
    },
    subsidyAmount: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'subsidy_amount',
      comment: '奖励金额',
      validate: {
        min: {
          args: [0],
          msg: '奖励金额不能为负数',
        },
      },
    },
    totalWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'total_wage',
      comment: '总工资 productive_wage + subsidy_amount + change_piece_wage + change_line_wage - fine_amount',
    },
    latheCount: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'lathe_count',
      comment: '车数',
      validate: {
        min: {
          args: [0],
          msg: '车数不能为负数',
        },
      },
    },
    remark: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '备注',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_wage',
    timestamps: true,
    underscored: true,
    comment: '工资记录表',
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'wage_enterprise_id_idx',
      },
      {
        fields: ['device_id'],
        name: 'wage_device_id_idx',
      },
      {
        fields: ['user_code'],
        name: 'wage_user_code_idx',
      },
      {
        fields: ['pattern_id'],
        name: 'wage_pattern_id_idx',
      },
      {
        fields: ['login_time'],
        name: 'wage_login_time_idx',
      },
      {
        fields: ['logout_time'],
        name: 'wage_logout_time_idx',
      },
      // 复合索引
      {
        fields: ['enterprise_id', 'user_code'],
        name: 'wage_enterprise_user_idx',
      },
      {
        fields: ['enterprise_id', 'device_id'],
        name: 'wage_enterprise_device_idx',
      },
      {
        fields: ['enterprise_id', 'login_time'],
        name: 'wage_enterprise_time_idx',
      },
    ],
  }
);

export default Wage;
