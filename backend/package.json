{"name": "embroidery-management-backend", "version": "1.0.0", "description": "刺绣管理系统后端API服务", "main": "dist/app.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:migrate:undo:all && npm run db:migrate && npm run db:seed"}, "keywords": ["embroidery", "management", "system", "nodejs", "express", "typescript"], "author": "Embroidery Management Team", "license": "MIT", "dependencies": {"@types/bcrypt": "^5.0.2", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}