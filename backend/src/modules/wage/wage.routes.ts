/**
 * 工资记录路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 11:15:00 +08:00; Reason: 创建工资记录路由配置, 集成权限中间件; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { wageController } from './wage.controller';
import { authMiddleware, permissionMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Wage:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 工资记录ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         deviceId:
 *           type: integer
 *           description: 设备ID
 *         userCode:
 *           type: integer
 *           description: 员工编码
 *         loginTime:
 *           type: string
 *           format: date-time
 *           description: 上机时间
 *         logoutTime:
 *           type: string
 *           format: date-time
 *           description: 下机时间
 *         patternId:
 *           type: integer
 *           description: 花样ID
 *         patternStitch:
 *           type: integer
 *           description: 花样针数
 *         patternWage:
 *           type: number
 *           description: 花样工价（元/万针）
 *         productiveWage:
 *           type: number
 *           description: 产量工资
 *         changePieceCount:
 *           type: integer
 *           description: 换片次数
 *         changePieceWage:
 *           type: number
 *           description: 换片工资
 *         changeLineCount:
 *           type: integer
 *           description: 换线次数
 *         changeLineWage:
 *           type: number
 *           description: 换线工资
 *         fineAmount:
 *           type: number
 *           description: 罚款金额
 *         subsidyAmount:
 *           type: number
 *           description: 奖励金额
 *         totalWage:
 *           type: number
 *           description: 总工资
 *         latheCount:
 *           type: number
 *           description: 车数
 *         remark:
 *           type: string
 *           description: 备注
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         device:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             code:
 *               type: string
 *         pattern:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 *             code:
 *               type: string
 *         enterprise:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *             name:
 *               type: string
 */

/**
 * @swagger
 * /api/v1/wages:
 *   get:
 *     summary: 获取工资记录列表
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: userCode
 *         schema:
 *           type: integer
 *         description: 员工编码
 *       - in: query
 *         name: deviceId
 *         schema:
 *           type: integer
 *         description: 设备ID
 *       - in: query
 *         name: patternId
 *         schema:
 *           type: integer
 *         description: 花样ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取工资记录列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     wages:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Wage'
 *                     total:
 *                       type: integer
 *                       description: 总数
 *                     page:
 *                       type: integer
 *                       description: 当前页码
 *                     pageSize:
 *                       type: integer
 *                       description: 每页数量
 *                     totalPages:
 *                       type: integer
 *                       description: 总页数
 */
router.get('/', authMiddleware, permissionMiddleware(['salary_list:view']), asyncHandler(wageController.getWageList));

/**
 * @swagger
 * /api/v1/wages/search-options:
 *   get:
 *     summary: 获取搜索选项
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取搜索选项成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     devices:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           code:
 *                             type: string
 *                     patterns:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           code:
 *                             type: string
 *                     users:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userCode:
 *                             type: integer
 *                           realName:
 *                             type: string
 */
router.get('/search-options', authMiddleware, permissionMiddleware(['salary_list:view']), asyncHandler(wageController.getSearchOptions));

/**
 * @swagger
 * /api/v1/wages/statistics:
 *   get:
 *     summary: 获取工资统计数据
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/statistics', authMiddleware, permissionMiddleware(['salary_list:view']), asyncHandler(wageController.getWageStatistics));

/**
 * @swagger
 * /api/v1/wages/{id}:
 *   get:
 *     summary: 获取工资记录详情
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工资记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取工资记录详情成功
 *                 data:
 *                   $ref: '#/components/schemas/Wage'
 *       404:
 *         description: 工资记录不存在
 */
router.get('/:id', authMiddleware, permissionMiddleware(['salary_list:view']), asyncHandler(wageController.getWageById));

/**
 * @swagger
 * /api/v1/wages:
 *   post:
 *     summary: 创建工资记录
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: integer
 *                 description: 设备ID
 *               userCode:
 *                 type: integer
 *                 description: 员工编码
 *               loginTime:
 *                 type: string
 *                 format: date-time
 *                 description: 上机时间
 *               logoutTime:
 *                 type: string
 *                 format: date-time
 *                 description: 下机时间
 *               patternId:
 *                 type: integer
 *                 description: 花样ID
 *               patternStitch:
 *                 type: integer
 *                 description: 花样针数
 *               patternWage:
 *                 type: number
 *                 description: 花样工价（元/万针）
 *               changePieceCount:
 *                 type: integer
 *                 description: 换片次数
 *               changePieceWage:
 *                 type: number
 *                 description: 换片工资
 *               changeLineCount:
 *                 type: integer
 *                 description: 换线次数
 *               changeLineWage:
 *                 type: number
 *                 description: 换线工资
 *               fineAmount:
 *                 type: number
 *                 description: 罚款金额
 *               subsidyAmount:
 *                 type: number
 *                 description: 奖励金额
 *               latheCount:
 *                 type: number
 *                 description: 车数
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建工资记录成功
 *                 data:
 *                   $ref: '#/components/schemas/Wage'
 *       400:
 *         description: 请求参数错误
 */
router.post('/', authMiddleware, permissionMiddleware(['salary_list:create']), asyncHandler(wageController.createWage));

/**
 * @swagger
 * /api/v1/wages/{id}:
 *   put:
 *     summary: 更新工资记录
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工资记录ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: integer
 *                 description: 设备ID
 *               userCode:
 *                 type: integer
 *                 description: 员工编码
 *               loginTime:
 *                 type: string
 *                 format: date-time
 *                 description: 上机时间
 *               logoutTime:
 *                 type: string
 *                 format: date-time
 *                 description: 下机时间
 *               patternId:
 *                 type: integer
 *                 description: 花样ID
 *               patternStitch:
 *                 type: integer
 *                 description: 花样针数
 *               patternWage:
 *                 type: number
 *                 description: 花样工价（元/万针）
 *               changePieceCount:
 *                 type: integer
 *                 description: 换片次数
 *               changePieceWage:
 *                 type: number
 *                 description: 换片工资
 *               changeLineCount:
 *                 type: integer
 *                 description: 换线次数
 *               changeLineWage:
 *                 type: number
 *                 description: 换线工资
 *               fineAmount:
 *                 type: number
 *                 description: 罚款金额
 *               subsidyAmount:
 *                 type: number
 *                 description: 奖励金额
 *               latheCount:
 *                 type: number
 *                 description: 车数
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新工资记录成功
 *                 data:
 *                   $ref: '#/components/schemas/Wage'
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 工资记录不存在
 */
router.put('/:id', authMiddleware, permissionMiddleware(['salary_list:update']), asyncHandler(wageController.updateWage));

/**
 * @swagger
 * /api/v1/wages/{id}:
 *   delete:
 *     summary: 删除工资记录
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 工资记录ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除工资记录成功
 *       404:
 *         description: 工资记录不存在
 */
router.delete('/:id', authMiddleware, permissionMiddleware(['salary_list:delete']), asyncHandler(wageController.deleteWage));

/**
 * @swagger
 * /api/v1/wages/batch/delete:
 *   post:
 *     summary: 批量删除工资记录
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - ids
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 工资记录ID数组
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 批量删除工资记录成功
 *       400:
 *         description: 请求参数错误
 */
router.post('/batch/delete', authMiddleware, permissionMiddleware(['salary_list:delete']), asyncHandler(wageController.batchDeleteWages));

/**
 * @swagger
 * /api/v1/wages/export:
 *   get:
 *     summary: 导出工资记录
 *     tags: [工资管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: userCode
 *         schema:
 *           type: integer
 *         description: 员工编码
 *       - in: query
 *         name: deviceId
 *         schema:
 *           type: integer
 *         description: 设备ID
 *       - in: query
 *         name: patternId
 *         schema:
 *           type: integer
 *         description: 花样ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 导出成功
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/export', authMiddleware, permissionMiddleware(['salary_list:export']), asyncHandler(wageController.exportWages));

export default router;
